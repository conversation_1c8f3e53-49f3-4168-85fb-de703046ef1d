import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:health_diary/health_service/service_manager.dart';

import '../../themes/app_theme.dart';

import 'controller/home_controller.dart';
import 'widgets/recent_records_list.dart';
import 'widgets/today_health_card.dart';

class HomePage extends ConsumerWidget {
  const HomePage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final todayOverviewAsync = ref.watch(homeControllerProvider);

    return Container(
      color: Theme.of(context).scaffoldBackgroundColor,
      child: RefreshIndicator(
        onRefresh: () async {
          ref.read(homeControllerProvider.notifier).refresh();
        },
        child: CustomScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          slivers: [
            SliverAppBar(
              expandedHeight: 120.0,
              floating: false,
              pinned: true,
              elevation: 0,
              backgroundColor: Theme.of(context).appBarTheme.backgroundColor,
              flexibleSpace: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomLeft,
                    colors: context.appColors.backgroundGradient,
                    stops: const [0, 0.2, 0.5, 1],
                  ),
                ),
                child: FlexibleSpaceBar(
                  background: Container(
                    alignment: Alignment.bottomLeft,
                    padding: const EdgeInsets.only(left: 16, bottom: 16),
                    child: Text('good_morning'.tr(), style: Theme.of(context).textTheme.headlineLarge),
                  ),
                ),
              ),
              leading: Container(
                margin: const EdgeInsets.only(left: 16),
                child: CircleAvatar(
                  backgroundColor: Theme.of(context).colorScheme.surfaceContainerHighest,
                  radius: 20,
                  child: Icon(Icons.person, color: Theme.of(context).colorScheme.onSurfaceVariant, size: 24),
                ),
              ),
              actions: [
                IconButton(
                  onPressed: () {
                    // TODO: navigate_to_statistics'.tr()
                  },
                  icon: FaIcon(FontAwesomeIcons.chartLine, color: Theme.of(context).colorScheme.onSurface, size: 20),
                ),
              ],
            ),
            SliverToBoxAdapter(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const SizedBox(height: 4),
                  // 今日健康概览卡片
                  todayOverviewAsync.when(
                    data: (overview) => TodayHealthCard(overview: overview),
                    loading: () => const _LoadingHealthCard(),
                    error: (error, stack) => TodayHealthCard(
                      overview: HealthServiceManager.emptyTodayOverview(),
                    ),
                  ),
                  const SizedBox(height: 16),
                ],
              ),
            ),
            // 最近记录列表
            SliverFillRemaining(
              child: RecentRecordsList(),
            ),
          ],
        ),
      ),
    );
  }
}

/// 加载中的健康卡片
class _LoadingHealthCard extends StatelessWidget {
  const _LoadingHealthCard();

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.secondary,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.favorite,
                color: Theme.of(context).colorScheme.onSecondary.withValues(alpha: 0.8),
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'today_health_overview'.tr(),
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: Theme.of(context).colorScheme.onSecondary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Center(
            child: CircularProgressIndicator(
              color: Theme.of(context).colorScheme.onSecondary,
            ),
          ),
          const SizedBox(height: 20),
        ],
      ),
    );
  }
}
